"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/employee/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/employee/TodoList.tsx":
/*!**********************************************!*\
  !*** ./src/components/employee/TodoList.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TodoList: () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ TodoList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst TodoList = (param)=>{\n    let { todos, onToggleTodo, onAddTodo } = param;\n    _s();\n    const [isAddingTodo, setIsAddingTodo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTodoTitle, setNewTodoTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newTodoPriority, setNewTodoPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'bg-red-50 text-red-700 border border-red-200 shadow-sm';\n            case 'medium':\n                return 'bg-amber-50 text-amber-700 border border-amber-200 shadow-sm';\n            case 'low':\n                return 'bg-emerald-50 text-emerald-700 border border-emerald-200 shadow-sm';\n            default:\n                return 'bg-gray-50 text-gray-700 border border-gray-200 shadow-sm';\n        }\n    };\n    const getPriorityIcon = (priority)=>{\n        switch(priority){\n            case 'high':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined);\n            case 'medium':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-amber-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined);\n            case 'low':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-emerald-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined);\n            case 'in-progress':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-blue-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-gray-400\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleAddTodo = ()=>{\n        if (newTodoTitle.trim()) {\n            onAddTodo(newTodoTitle.trim(), newTodoPriority);\n            setNewTodoTitle('');\n            setNewTodoPriority('medium');\n            setIsAddingTodo(false);\n        }\n    };\n    const pendingTodos = todos.filter((todo)=>todo.status !== 'completed');\n    const completedTodos = todos.filter((todo)=>todo.status === 'completed');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Todo List\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIsAddingTodo(true),\n                                    disabled: isAddingTodo,\n                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Add Todo\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/employee/todos\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Edit Todos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: [\n                    isAddingTodo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Todo Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Enter todo title...\",\n                                            value: newTodoTitle,\n                                            onChange: (e)=>setNewTodoTitle(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            autoFocus: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Priority Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: newTodoPriority,\n                                                    onChange: (e)=>setNewTodoPriority(e.target.value),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"low\",\n                                                            children: \"\\uD83D\\uDFE2 Low Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"medium\",\n                                                            children: \"\\uD83D\\uDFE1 Medium Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"high\",\n                                                            children: \"\\uD83D\\uDD34 High Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: handleAddTodo,\n                                                    className: \"bg-blue-600 hover:bg-blue-700 px-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-2\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Add\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>{\n                                                        setIsAddingTodo(false);\n                                                        setNewTodoTitle('');\n                                                        setNewTodoPriority('medium');\n                                                    },\n                                                    className: \"px-6\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined),\n                    pendingTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-800 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 text-blue-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Pending Tasks (\",\n                                    pendingTodos.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: pendingTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-blue-200 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0 hover:scale-110 transition-transform\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-900 truncate mb-1\",\n                                                        children: todo.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    todo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        children: todo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    todo.dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"Due: \",\n                                                            new Date(todo.dueDate).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full \".concat(getPriorityColor(todo.priority)),\n                                                    children: [\n                                                        getPriorityIcon(todo.priority),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: todo.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    completedTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-800 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 text-green-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Completed Tasks (\",\n                                    completedTodos.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: completedTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group flex items-center space-x-4 p-4 bg-green-50 border border-green-200 rounded-xl opacity-80 hover:opacity-100 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0 hover:scale-110 transition-transform\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-600 line-through truncate mb-1\",\n                                                        children: todo.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    todo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 line-through truncate\",\n                                                        children: todo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full opacity-60 \".concat(getPriorityColor(todo.priority)),\n                                                    children: [\n                                                        getPriorityIcon(todo.priority),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: todo.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, undefined),\n                    todos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"No todos yet. Add your first task!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TodoList, \"no1A+ddmKmtQjwsqteIZeZWyLSA=\");\n_c = TodoList;\nvar _c;\n$RefreshReg$(_c, \"TodoList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/employee/TodoList.tsx\n"));

/***/ })

});