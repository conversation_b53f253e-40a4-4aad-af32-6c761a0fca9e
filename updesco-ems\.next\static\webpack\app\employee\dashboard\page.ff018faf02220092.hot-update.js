"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/employee/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/employee/TodoList.tsx":
/*!**********************************************!*\
  !*** ./src/components/employee/TodoList.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TodoList: () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ TodoList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst TodoList = (param)=>{\n    let { todos, onToggleTodo, onAddTodo } = param;\n    _s();\n    const [isAddingTodo, setIsAddingTodo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTodoTitle, setNewTodoTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newTodoPriority, setNewTodoPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'bg-red-50 text-red-700 border border-red-200 shadow-sm';\n            case 'medium':\n                return 'bg-amber-50 text-amber-700 border border-amber-200 shadow-sm';\n            case 'low':\n                return 'bg-emerald-50 text-emerald-700 border border-emerald-200 shadow-sm';\n            default:\n                return 'bg-gray-50 text-gray-700 border border-gray-200 shadow-sm';\n        }\n    };\n    const getPriorityIcon = (priority)=>{\n        switch(priority){\n            case 'high':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined);\n            case 'medium':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-amber-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined);\n            case 'low':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-emerald-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined);\n            case 'in-progress':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-blue-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-gray-400\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleAddTodo = ()=>{\n        if (newTodoTitle.trim()) {\n            onAddTodo(newTodoTitle.trim(), newTodoPriority);\n            setNewTodoTitle('');\n            setNewTodoPriority('medium');\n            setIsAddingTodo(false);\n        }\n    };\n    const pendingTodos = todos.filter((todo)=>todo.status !== 'completed');\n    const completedTodos = todos.filter((todo)=>todo.status === 'completed');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Todo List\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIsAddingTodo(true),\n                                    disabled: isAddingTodo,\n                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Add Todo\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/employee/todos\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Edit Todos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: [\n                    isAddingTodo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Todo Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Enter todo title...\",\n                                            value: newTodoTitle,\n                                            onChange: (e)=>setNewTodoTitle(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            autoFocus: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Priority Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: newTodoPriority,\n                                                    onChange: (e)=>setNewTodoPriority(e.target.value),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"low\",\n                                                            children: \"\\uD83D\\uDFE2 Low Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"medium\",\n                                                            children: \"\\uD83D\\uDFE1 Medium Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"high\",\n                                                            children: \"\\uD83D\\uDD34 High Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: handleAddTodo,\n                                                    className: \"bg-blue-600 hover:bg-blue-700 px-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-2\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Add\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>{\n                                                        setIsAddingTodo(false);\n                                                        setNewTodoTitle('');\n                                                        setNewTodoPriority('medium');\n                                                    },\n                                                    className: \"px-6\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined),\n                    pendingTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-800 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 text-blue-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Pending Tasks (\",\n                                    pendingTodos.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: pendingTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-blue-200 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0 hover:scale-110 transition-transform\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-900 truncate mb-1\",\n                                                        children: todo.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    todo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        children: todo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    todo.dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"Due: \",\n                                                            new Date(todo.dueDate).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full \".concat(getPriorityColor(todo.priority)),\n                                                    children: [\n                                                        getPriorityIcon(todo.priority),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: todo.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    completedTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Completed Tasks\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: completedTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg opacity-75\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600 line-through truncate\",\n                                                    children: todo.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(todo.priority)),\n                                                children: todo.priority\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, undefined),\n                    todos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"No todos yet. Add your first task!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TodoList, \"no1A+ddmKmtQjwsqteIZeZWyLSA=\");\n_c = TodoList;\nvar _c;\n$RefreshReg$(_c, \"TodoList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/employee/TodoList.tsx\n"));

/***/ })

});