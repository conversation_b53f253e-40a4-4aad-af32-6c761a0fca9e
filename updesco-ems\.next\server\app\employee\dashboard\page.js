/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/employee/dashboard/page";
exports.ids = ["app/employee/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employee/dashboard/page.tsx */ \"(rsc)/./src/app/employee/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'employee',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/employee/dashboard/page\",\n        pathname: \"/employee/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employee/dashboard/page.tsx */ \"(rsc)/./src/app/employee/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VqandhbCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q1VQREVTQ08lNUMlNUN1cGRlc2NvLWVtcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2VtcGxveWVlJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUF3SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVWpqd2FsXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcVVBERVNDT1xcXFx1cGRlc2NvLWVtc1xcXFxzcmNcXFxcYXBwXFxcXGVtcGxveWVlXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVWpqd2FsXFxPbmVEcml2ZVxcRGVza3RvcFxcVVBERVNDT1xcdXBkZXNjby1lbXNcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/employee/dashboard/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/employee/dashboard/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\UPDESCO\\updesco-ems\\src\\app\\employee\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"478f7b9a9a06\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVqandhbFxcT25lRHJpdmVcXERlc2t0b3BcXFVQREVTQ09cXHVwZGVzY28tZW1zXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0NzhmN2I5YTlhMDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVWpqd2FsXFxPbmVEcml2ZVxcRGVza3RvcFxcVVBERVNDT1xcdXBkZXNjby1lbXNcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDcmVhdGUgTmV4dCBBcHBcIixcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employee/dashboard/page.tsx */ \"(ssr)/./src/app/employee/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VqandhbCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q1VQREVTQ08lNUMlNUN1cGRlc2NvLWVtcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2VtcGxveWVlJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUF3SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVWpqd2FsXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcVVBERVNDT1xcXFx1cGRlc2NvLWVtc1xcXFxzcmNcXFxcYXBwXFxcXGVtcGxveWVlXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUjjwal%5C%5COneDrive%5C%5CDesktop%5C%5CUPDESCO%5C%5Cupdesco-ems%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/employee/dashboard/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/employee/dashboard/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmployeeDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Layout */ \"(ssr)/./src/components/layout/Layout.tsx\");\n/* harmony import */ var _components_employee_CheckInOutBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/employee/CheckInOutBox */ \"(ssr)/./src/components/employee/CheckInOutBox.tsx\");\n/* harmony import */ var _components_employee_Calendar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/employee/Calendar */ \"(ssr)/./src/components/employee/Calendar.tsx\");\n/* harmony import */ var _components_employee_TodoList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/employee/TodoList */ \"(ssr)/./src/components/employee/TodoList.tsx\");\n/* harmony import */ var _components_employee_LeaveStatusTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/employee/LeaveStatusTable */ \"(ssr)/./src/components/employee/LeaveStatusTable.tsx\");\n/* harmony import */ var _components_employee_StatsBoxes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/employee/StatsBoxes */ \"(ssr)/./src/components/employee/StatsBoxes.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction EmployeeDashboard() {\n    // Mock data - In a real app, this would come from an API\n    const [todayAttendance, setTodayAttendance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [attendanceRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            employeeId: 'emp1',\n            date: '2024-01-15',\n            checkInTime: '09:00',\n            checkOutTime: '17:30',\n            status: 'present',\n            workingHours: 8.5\n        },\n        {\n            id: '2',\n            employeeId: 'emp1',\n            date: '2024-01-16',\n            checkInTime: '09:15',\n            checkOutTime: '17:45',\n            status: 'late',\n            workingHours: 8.5\n        },\n        {\n            id: '3',\n            employeeId: 'emp1',\n            date: '2024-01-17',\n            status: 'absent'\n        }\n    ]);\n    const [todos, setTodos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            employeeId: 'emp1',\n            title: 'Complete project documentation',\n            description: 'Finish the technical documentation for the new feature',\n            priority: 'high',\n            status: 'in-progress',\n            dueDate: '2024-01-20',\n            createdDate: '2024-01-15'\n        },\n        {\n            id: '2',\n            employeeId: 'emp1',\n            title: 'Review code changes',\n            priority: 'medium',\n            status: 'pending',\n            createdDate: '2024-01-16'\n        },\n        {\n            id: '3',\n            employeeId: 'emp1',\n            title: 'Attend team meeting',\n            priority: 'low',\n            status: 'completed',\n            createdDate: '2024-01-14'\n        }\n    ]);\n    const [leaveRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            employeeId: 'emp1',\n            startDate: '2024-01-25',\n            endDate: '2024-01-26',\n            type: 'vacation',\n            reason: 'Family vacation',\n            status: 'approved',\n            appliedDate: '2024-01-10',\n            approvedBy: 'manager1'\n        },\n        {\n            id: '2',\n            employeeId: 'emp1',\n            startDate: '2024-02-05',\n            endDate: '2024-02-05',\n            type: 'sick',\n            reason: 'Medical appointment',\n            status: 'pending',\n            appliedDate: '2024-01-18'\n        }\n    ]);\n    const [workingDaysStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        presentDays: 18,\n        leaveDays: 2,\n        totalWorkingDays: 22,\n        currentMonth: 'January 2024'\n    });\n    const handleCheckIn = (time)=>{\n        const today = new Date().toISOString().split('T')[0];\n        const newAttendance = {\n            id: Date.now().toString(),\n            employeeId: 'emp1',\n            date: today,\n            checkInTime: time,\n            status: 'present'\n        };\n        setTodayAttendance(newAttendance);\n    };\n    const handleCheckOut = (time)=>{\n        if (todayAttendance) {\n            const updatedAttendance = {\n                ...todayAttendance,\n                checkOutTime: time\n            };\n            setTodayAttendance(updatedAttendance);\n        }\n    };\n    const handleToggleTodo = (todoId)=>{\n        setTodos((prevTodos)=>prevTodos.map((todo)=>todo.id === todoId ? {\n                    ...todo,\n                    status: todo.status === 'completed' ? 'pending' : 'completed'\n                } : todo));\n    };\n    const handleAddTodo = (title, priority)=>{\n        const newTodo = {\n            id: Date.now().toString(),\n            employeeId: 'emp1',\n            title,\n            priority,\n            status: 'pending',\n            createdDate: new Date().toISOString().split('T')[0]\n        };\n        setTodos((prevTodos)=>[\n                newTodo,\n                ...prevTodos\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_2__.Layout, {\n        employeeName: \"John Doe\",\n        profilePicture: \"\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employee_CheckInOutBox__WEBPACK_IMPORTED_MODULE_3__.CheckInOutBox, {\n                    todayAttendance: todayAttendance,\n                    onCheckIn: handleCheckIn,\n                    onCheckOut: handleCheckOut\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employee_Calendar__WEBPACK_IMPORTED_MODULE_4__.Calendar, {\n                            attendanceRecords: attendanceRecords\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employee_TodoList__WEBPACK_IMPORTED_MODULE_5__.TodoList, {\n                            todos: todos,\n                            onToggleTodo: handleToggleTodo,\n                            onAddTodo: handleAddTodo\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employee_LeaveStatusTable__WEBPACK_IMPORTED_MODULE_6__.LeaveStatusTable, {\n                    leaveRequests: leaveRequests\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                            children: \"Monthly Statistics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_employee_StatsBoxes__WEBPACK_IMPORTED_MODULE_7__.StatsBoxes, {\n                            stats: workingDaysStats\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/employee/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/employee/Calendar.tsx":
/*!**********************************************!*\
  !*** ./src/components/employee/Calendar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Calendar: () => (/* binding */ Calendar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Calendar auto */ \n\n\n\nconst Calendar = ({ attendanceRecords })=>{\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const getDaysInMonth = (date)=>{\n        return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\n    };\n    const getFirstDayOfMonth = (date)=>{\n        return new Date(date.getFullYear(), date.getMonth(), 1).getDay();\n    };\n    const getAttendanceForDate = (day)=>{\n        const dateString = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n        return attendanceRecords.find((record)=>record.date === dateString);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'present':\n                return 'bg-emerald-100 text-emerald-900 border-emerald-200';\n            case 'absent':\n                return 'bg-red-100 text-red-900 border-red-200';\n            case 'late':\n                return 'bg-amber-100 text-amber-900 border-amber-200';\n            case 'half-day':\n                return 'bg-orange-100 text-orange-900 border-orange-200';\n            default:\n                return 'bg-gray-50 text-gray-800 border-gray-200';\n        }\n    };\n    const navigateMonth = (direction)=>{\n        setCurrentDate((prev)=>{\n            const newDate = new Date(prev);\n            if (direction === 'prev') {\n                newDate.setMonth(prev.getMonth() - 1);\n            } else {\n                newDate.setMonth(prev.getMonth() + 1);\n            }\n            return newDate;\n        });\n    };\n    const daysInMonth = getDaysInMonth(currentDate);\n    const firstDay = getFirstDayOfMonth(currentDate);\n    const today = new Date();\n    const isCurrentMonth = currentDate.getMonth() === today.getMonth() && currentDate.getFullYear() === today.getFullYear();\n    const monthNames = [\n        'January',\n        'February',\n        'March',\n        'April',\n        'May',\n        'June',\n        'July',\n        'August',\n        'September',\n        'October',\n        'November',\n        'December'\n    ];\n    const dayNames = [\n        'Sun',\n        'Mon',\n        'Tue',\n        'Wed',\n        'Thu',\n        'Fri',\n        'Sat'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v14a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Attendance Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>navigateMonth('prev'),\n                                    className: \"hover:bg-blue-50 hover:border-blue-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-bold min-w-[160px] text-center text-gray-900 bg-blue-50 px-4 py-2 rounded-lg\",\n                                    children: [\n                                        monthNames[currentDate.getMonth()],\n                                        \" \",\n                                        currentDate.getFullYear()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>navigateMonth('next'),\n                                    className: \"hover:bg-blue-50 hover:border-blue-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9 5l7 7-7 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 gap-1 mb-3\",\n                        children: dayNames.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 text-center text-sm font-bold text-gray-800 bg-gray-50 rounded-lg\",\n                                children: day\n                            }, day, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 gap-1\",\n                        children: [\n                            Array.from({\n                                length: firstDay\n                            }, (_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 h-12\"\n                                }, `empty-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)),\n                            Array.from({\n                                length: daysInMonth\n                            }, (_, index)=>{\n                                const day = index + 1;\n                                const attendance = getAttendanceForDate(day);\n                                const isToday = isCurrentMonth && day === today.getDate();\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 h-14 flex items-center justify-center text-sm relative rounded-xl border transition-all duration-200 cursor-pointer ${isToday ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-300 shadow-md' : attendance ? `${getStatusColor(attendance.status)} border shadow-sm hover:shadow-md` : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `font-semibold ${isToday ? 'text-blue-900 text-base' : attendance ? '' : 'text-gray-800'}`,\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        attendance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-1 right-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-2.5 h-2.5 rounded-full shadow-sm ${attendance.status === 'present' ? 'bg-emerald-500' : attendance.status === 'absent' ? 'bg-red-500' : attendance.status === 'late' ? 'bg-amber-500' : 'bg-orange-500'}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, day, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-700 mb-3\",\n                                children: \"Legend\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 p-2 bg-emerald-50 rounded-lg border border-emerald-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full bg-emerald-500 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-emerald-800\",\n                                                children: \"Present\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 p-2 bg-red-50 rounded-lg border border-red-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full bg-red-500 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Absent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 p-2 bg-amber-50 rounded-lg border border-amber-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full bg-amber-500 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-amber-800\",\n                                                children: \"Late\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 p-2 bg-orange-50 rounded-lg border border-orange-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full bg-orange-500 shadow-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-orange-800\",\n                                                children: \"Half Day\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\Calendar.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/employee/Calendar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/employee/CheckInOutBox.tsx":
/*!***************************************************!*\
  !*** ./src/components/employee/CheckInOutBox.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckInOutBox: () => (/* binding */ CheckInOutBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _CheckInOutModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CheckInOutModal */ \"(ssr)/./src/components/employee/CheckInOutModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ CheckInOutBox auto */ \n\n\n\n\nconst CheckInOutBox = ({ todayAttendance, onCheckIn, onCheckOut })=>{\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const formatTime = (timeString)=>{\n        if (!timeString) return '--:--';\n        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const getWorkingHours = ()=>{\n        if (!todayAttendance?.checkInTime || !todayAttendance?.checkOutTime) {\n            return '--:--';\n        }\n        const checkIn = new Date(`2000-01-01T${todayAttendance.checkInTime}`);\n        const checkOut = new Date(`2000-01-01T${todayAttendance.checkOutTime}`);\n        const diffMs = checkOut.getTime() - checkIn.getTime();\n        const diffHours = diffMs / (1000 * 60 * 60);\n        const hours = Math.floor(diffHours);\n        const minutes = Math.floor((diffHours - hours) * 60);\n        return `${hours}h ${minutes}m`;\n    };\n    const canCheckOut = todayAttendance?.checkInTime && !todayAttendance?.checkOutTime;\n    const canCheckIn = !todayAttendance?.checkInTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-center text-blue-600\",\n                                    children: \"Check-in Time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: formatTime(todayAttendance?.checkInTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: todayAttendance?.checkInTime ? 'Checked in' : 'Not checked in'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-center text-blue-600\",\n                                    children: \"Check-out Time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: formatTime(todayAttendance?.checkOutTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: todayAttendance?.checkOutTime ? 'Checked out' : 'Not checked out'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-center text-blue-600\",\n                                    children: \"Working Hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: getWorkingHours()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Today's total\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: canCheckIn ? 'Ready to start your day?' : canCheckOut ? 'Ready to end your day?' : 'All done for today!'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined),\n                            (canCheckIn || canCheckOut) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsModalOpen(true),\n                                size: \"lg\",\n                                className: \"px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    canCheckIn ? 'Check In' : 'Check Out'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, undefined),\n                            !canCheckIn && !canCheckOut && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-green-600 font-medium\",\n                                children: \"✓ Attendance completed for today\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CheckInOutModal__WEBPACK_IMPORTED_MODULE_4__.CheckInOutModal, {\n                isOpen: isModalOpen,\n                onClose: ()=>setIsModalOpen(false),\n                type: canCheckIn ? 'check-in' : 'check-out',\n                onSubmit: canCheckIn ? onCheckIn : onCheckOut\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutBox.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/employee/CheckInOutBox.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/employee/CheckInOutModal.tsx":
/*!*****************************************************!*\
  !*** ./src/components/employee/CheckInOutModal.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckInOutModal: () => (/* binding */ CheckInOutModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Modal */ \"(ssr)/./src/components/ui/Modal.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ CheckInOutModal auto */ \n\n\n\nconst CheckInOutModal = ({ isOpen, onClose, type, onSubmit })=>{\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customTime, setCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [useCustomTime, setUseCustomTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckInOutModal.useEffect\": ()=>{\n            const updateTime = {\n                \"CheckInOutModal.useEffect.updateTime\": ()=>{\n                    const now = new Date();\n                    const timeString = now.toTimeString().slice(0, 5); // HH:MM format\n                    setCurrentTime(timeString);\n                    if (!useCustomTime) {\n                        setCustomTime(timeString);\n                    }\n                }\n            }[\"CheckInOutModal.useEffect.updateTime\"];\n            updateTime();\n            const interval = setInterval(updateTime, 1000);\n            return ({\n                \"CheckInOutModal.useEffect\": ()=>clearInterval(interval)\n            })[\"CheckInOutModal.useEffect\"];\n        }\n    }[\"CheckInOutModal.useEffect\"], [\n        useCustomTime\n    ]);\n    const handleSubmit = ()=>{\n        const timeToSubmit = useCustomTime ? customTime : currentTime;\n        if (timeToSubmit) {\n            onSubmit(timeToSubmit);\n            onClose();\n            setUseCustomTime(false);\n            setCustomTime('');\n        }\n    };\n    const getCurrentDateTime = ()=>{\n        const now = new Date();\n        return now.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const title = type === 'check-in' ? 'Check In' : 'Check Out';\n    const actionText = type === 'check-in' ? 'Check In' : 'Check Out';\n    const icon = type === 'check-in' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-16 h-16 text-green-500 mx-auto mb-4\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-16 h-16 text-blue-500 mx-auto mb-4\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: title,\n        size: \"md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                icon,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: type === 'check-in' ? 'Good morning!' : 'End of workday?'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: getCurrentDateTime()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-2\",\n                            children: \"Current Time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: new Date().toLocaleTimeString('en-US', {\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                second: '2-digit',\n                                hour12: true\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"customTime\",\n                                    checked: useCustomTime,\n                                    onChange: (e)=>setUseCustomTime(e.target.checked),\n                                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customTime\",\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Use custom time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined),\n                        useCustomTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Custom Time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"time\",\n                                    value: customTime,\n                                    onChange: (e)=>setCustomTime(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: onClose,\n                            className: \"flex-1\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSubmit,\n                            className: \"flex-1\",\n                            children: actionText\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\CheckInOutModal.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/employee/CheckInOutModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/employee/LeaveStatusTable.tsx":
/*!******************************************************!*\
  !*** ./src/components/employee/LeaveStatusTable.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeaveStatusTable: () => (/* binding */ LeaveStatusTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ LeaveStatusTable auto */ \n\n\nconst LeaveStatusTable = ({ leaveRequests })=>{\n    const getStatusBadge = (status)=>{\n        const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';\n        switch(status){\n            case 'approved':\n                return `${baseClasses} bg-green-100 text-green-800`;\n            case 'rejected':\n                return `${baseClasses} bg-red-100 text-red-800`;\n            case 'pending':\n                return `${baseClasses} bg-yellow-100 text-yellow-800`;\n            default:\n                return `${baseClasses} bg-gray-100 text-gray-800`;\n        }\n    };\n    const getLeaveTypeBadge = (type)=>{\n        const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';\n        switch(type){\n            case 'sick':\n                return `${baseClasses} bg-red-50 text-red-700 border border-red-200`;\n            case 'vacation':\n                return `${baseClasses} bg-blue-50 text-blue-700 border border-blue-200`;\n            case 'personal':\n                return `${baseClasses} bg-purple-50 text-purple-700 border border-purple-200`;\n            case 'emergency':\n                return `${baseClasses} bg-orange-50 text-orange-700 border border-orange-200`;\n            default:\n                return `${baseClasses} bg-gray-50 text-gray-700 border border-gray-200`;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    const calculateDays = (startDate, endDate)=>{\n        const start = new Date(startDate);\n        const end = new Date(endDate);\n        const diffTime = Math.abs(end.getTime() - start.getTime());\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;\n        return diffDays;\n    };\n    // Sort leave requests by applied date (most recent first)\n    const sortedLeaveRequests = [\n        ...leaveRequests\n    ].sort((a, b)=>new Date(b.appliedDate).getTime() - new Date(a.appliedDate).getTime());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    children: \"Leave Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: sortedLeaveRequests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"text-left py-3 px-2 font-medium text-gray-700\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"text-left py-3 px-2 font-medium text-gray-700\",\n                                            children: \"Duration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"text-left py-3 px-2 font-medium text-gray-700\",\n                                            children: \"Days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"text-left py-3 px-2 font-medium text-gray-700\",\n                                            children: \"Applied\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"text-left py-3 px-2 font-medium text-gray-700\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"text-left py-3 px-2 font-medium text-gray-700\",\n                                            children: \"Reason\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: sortedLeaveRequests.map((leave)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-3 px-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: getLeaveTypeBadge(leave.type),\n                                                    children: leave.type.charAt(0).toUpperCase() + leave.type.slice(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-3 px-2 text-gray-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        formatDate(leave.startDate),\n                                                        leave.startDate !== leave.endDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \" to \"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                                    lineNumber: 95,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                formatDate(leave.endDate)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-3 px-2 text-gray-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        calculateDays(leave.startDate, leave.endDate),\n                                                        \" day\",\n                                                        calculateDays(leave.startDate, leave.endDate) > 1 ? 's' : ''\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-3 px-2 text-gray-600\",\n                                                children: formatDate(leave.appliedDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-3 px-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: getStatusBadge(leave.status),\n                                                    children: leave.status.charAt(0).toUpperCase() + leave.status.slice(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-3 px-2 text-gray-600 max-w-xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"truncate\",\n                                                    title: leave.reason,\n                                                    children: leave.reason\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, leave.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v14a2 2 0 002 2z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No leave requests found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mt-1\",\n                            children: \"Your leave applications will appear here\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\LeaveStatusTable.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/employee/LeaveStatusTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/employee/StatsBoxes.tsx":
/*!************************************************!*\
  !*** ./src/components/employee/StatsBoxes.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsBoxes: () => (/* binding */ StatsBoxes)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ StatsBoxes auto */ \n\n\nconst StatsBoxes = ({ stats })=>{\n    const attendancePercentage = stats.totalWorkingDays > 0 ? Math.round(stats.presentDays / stats.totalWorkingDays * 100) : 0;\n    const getAttendanceColor = (percentage)=>{\n        if (percentage >= 90) return 'text-green-600';\n        if (percentage >= 75) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const statsData = [\n        {\n            title: 'Present Days',\n            value: stats.presentDays,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-green-500\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined),\n            bgColor: 'bg-green-50',\n            borderColor: 'border-green-200',\n            textColor: 'text-green-600'\n        },\n        {\n            title: 'Leave Days',\n            value: stats.leaveDays,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-red-500\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v14a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined),\n            bgColor: 'bg-red-50',\n            borderColor: 'border-red-200',\n            textColor: 'text-red-600'\n        },\n        {\n            title: 'Total Working Days',\n            value: stats.totalWorkingDays,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-500\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            bgColor: 'bg-blue-50',\n            borderColor: 'border-blue-200',\n            textColor: 'text-blue-600'\n        },\n        {\n            title: 'Attendance Rate',\n            value: `${attendancePercentage}%`,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined),\n            bgColor: attendancePercentage >= 90 ? 'bg-green-50' : attendancePercentage >= 75 ? 'bg-yellow-50' : 'bg-red-50',\n            borderColor: attendancePercentage >= 90 ? 'border-green-200' : attendancePercentage >= 75 ? 'border-yellow-200' : 'border-red-200',\n            textColor: getAttendanceColor(attendancePercentage)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: statsData.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: `${stat.bgColor} ${stat.borderColor} border-2`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-3xl font-bold ${stat.textColor}`,\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        stat.title === 'Attendance Rate' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"This month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: stat.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined),\n                        stat.title === 'Attendance Rate' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `h-2 rounded-full transition-all duration-300 ${attendancePercentage >= 90 ? 'bg-green-500' : attendancePercentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`,\n                                    style: {\n                                        width: `${attendancePercentage}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\StatsBoxes.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/employee/StatsBoxes.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/employee/TodoList.tsx":
/*!**********************************************!*\
  !*** ./src/components/employee/TodoList.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TodoList: () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ TodoList auto */ \n\n\n\n\nconst TodoList = ({ todos, onToggleTodo, onAddTodo })=>{\n    const [isAddingTodo, setIsAddingTodo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTodoTitle, setNewTodoTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newTodoPriority, setNewTodoPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'bg-red-50 text-red-700 border border-red-200 shadow-sm';\n            case 'medium':\n                return 'bg-amber-50 text-amber-700 border border-amber-200 shadow-sm';\n            case 'low':\n                return 'bg-emerald-50 text-emerald-700 border border-emerald-200 shadow-sm';\n            default:\n                return 'bg-gray-50 text-gray-700 border border-gray-200 shadow-sm';\n        }\n    };\n    const getPriorityIcon = (priority)=>{\n        switch(priority){\n            case 'high':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined);\n            case 'medium':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-amber-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined);\n            case 'low':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-emerald-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined);\n            case 'in-progress':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-blue-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-gray-400\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleAddTodo = ()=>{\n        if (newTodoTitle.trim()) {\n            onAddTodo(newTodoTitle.trim(), newTodoPriority);\n            setNewTodoTitle('');\n            setNewTodoPriority('medium');\n            setIsAddingTodo(false);\n        }\n    };\n    const pendingTodos = todos.filter((todo)=>todo.status !== 'completed');\n    const completedTodos = todos.filter((todo)=>todo.status === 'completed');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Todo List\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIsAddingTodo(true),\n                                    disabled: isAddingTodo,\n                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Add Todo\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/employee/todos\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-600 hover:bg-blue-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Edit Todos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: [\n                    isAddingTodo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Todo Title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Enter todo title...\",\n                                            value: newTodoTitle,\n                                            onChange: (e)=>setNewTodoTitle(e.target.value),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                            autoFocus: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Priority Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: newTodoPriority,\n                                                    onChange: (e)=>setNewTodoPriority(e.target.value),\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"low\",\n                                                            children: \"\\uD83D\\uDFE2 Low Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"medium\",\n                                                            children: \"\\uD83D\\uDFE1 Medium Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"high\",\n                                                            children: \"\\uD83D\\uDD34 High Priority\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: handleAddTodo,\n                                                    className: \"bg-blue-600 hover:bg-blue-700 px-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 mr-2\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Add\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>{\n                                                        setIsAddingTodo(false);\n                                                        setNewTodoTitle('');\n                                                        setNewTodoPriority('medium');\n                                                    },\n                                                    className: \"px-6\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined),\n                    pendingTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-800 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 text-blue-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Pending Tasks (\",\n                                    pendingTodos.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: pendingTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md hover:border-blue-200 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0 hover:scale-110 transition-transform\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-900 truncate mb-1\",\n                                                        children: todo.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    todo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        children: todo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    todo.dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"Due: \",\n                                                            new Date(todo.dueDate).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex items-center px-3 py-1 text-xs font-medium rounded-full ${getPriorityColor(todo.priority)}`,\n                                                    children: [\n                                                        getPriorityIcon(todo.priority),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: todo.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    completedTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-800 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 text-green-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Completed Tasks (\",\n                                    completedTodos.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: completedTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group flex items-center space-x-4 p-4 bg-green-50 border border-green-200 rounded-xl opacity-80 hover:opacity-100 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0 hover:scale-110 transition-transform\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-600 line-through truncate mb-1\",\n                                                        children: todo.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    todo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 line-through truncate\",\n                                                        children: todo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex items-center px-3 py-1 text-xs font-medium rounded-full opacity-60 ${getPriorityColor(todo.priority)}`,\n                                                    children: [\n                                                        getPriorityIcon(todo.priority),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: todo.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, undefined),\n                    todos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"No todos yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-4\",\n                                children: \"Start organizing your tasks by adding your first todo!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsAddingTodo(true),\n                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Add Your First Todo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/employee/TodoList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Layout: () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TopBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TopBar */ \"(ssr)/./src/components/layout/TopBar.tsx\");\n/* harmony import */ var _SideBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SideBar */ \"(ssr)/./src/components/layout/SideBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Layout auto */ \n\n\n\nconst Layout = ({ children, employeeName, profilePicture })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBar__WEBPACK_IMPORTED_MODULE_3__.SideBar, {\n                    employeeName: employeeName,\n                    profilePicture: profilePicture\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopBar__WEBPACK_IMPORTED_MODULE_2__.TopBar, {\n                        employeeName: employeeName,\n                        profilePicture: profilePicture\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/SideBar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/SideBar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SideBar: () => (/* binding */ SideBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ SideBar auto */ \n\n\n\n\nconst SideBar = ({ employeeName, profilePicture })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navItems = [\n        {\n            name: 'Dashboard',\n            href: '/employee/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            name: 'Attendance',\n            href: '/employee/attendance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            name: 'Leave',\n            href: '/employee/leave',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v14a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            name: 'Todos',\n            href: '/employee/todos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }\n    ];\n    const handleLogout = ()=>{\n        // TODO: Implement logout functionality\n        console.log('Logout clicked');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white h-full w-64 shadow-lg border-r border-gray-200 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-lg\",\n                                children: \"U\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-blue-600\",\n                                    children: \"UPDESCO\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Employee Portal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2\",\n                    children: navItems.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${isActive ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden\",\n                                children: profilePicture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: profilePicture,\n                                    alt: employeeName,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 font-medium\",\n                                    children: employeeName.split(' ').map((n)=>n[0]).join('').toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                        children: employeeName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Employee\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: handleLogout,\n                        className: \"w-full text-red-600 border-red-600 hover:bg-red-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 mr-2\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Logout\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\SideBar.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/SideBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/TopBar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/TopBar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TopBar: () => (/* binding */ TopBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ TopBar auto */ \n\n\nconst TopBar = ({ employeeName, profilePicture })=>{\n    const getCurrentDate = ()=>{\n        const now = new Date();\n        const options = {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        };\n        return now.toLocaleDateString('en-US', options);\n    };\n    const handleLogout = ()=>{\n        // TODO: Implement logout functionality\n        console.log('Logout clicked');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: [\n                            \"Welcome, \",\n                            employeeName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium\",\n                                children: getCurrentDate()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden\",\n                                    children: profilePicture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: profilePicture,\n                                        alt: employeeName,\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 font-medium text-sm\",\n                                        children: employeeName.split(' ').map((n)=>n[0]).join('').toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleLogout,\n                                    className: \"text-red-600 border-red-600 hover:bg-red-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Logout\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\layout\\\\TopBar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/TopBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Button = ({ variant = 'primary', size = 'md', children, className = '', ...props })=>{\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'bg-blue-600 text-white hover:bg-blue-700',\n        secondary: 'bg-gray-600 text-white hover:bg-gray-700',\n        outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50',\n        ghost: 'text-blue-600 hover:bg-blue-50'\n    };\n    const sizeClasses = {\n        sm: 'px-3 py-1.5 text-sm',\n        md: 'px-4 py-2 text-sm',\n        lg: 'px-6 py-3 text-base'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = ({ children, className = '', padding = 'md' })=>{\n    const paddingClasses = {\n        sm: 'p-4',\n        md: 'p-6',\n        lg: 'p-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg shadow-md border border-gray-200 ${paddingClasses[padding]} ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardHeader = ({ children, className = '' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mb-4 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardTitle = ({ children, className = '' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        className: `text-lg font-semibold text-gray-900 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardContent = ({ children, className = '' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMEI7QUFRbkIsTUFBTUMsT0FBNEIsQ0FBQyxFQUN4Q0MsUUFBUSxFQUNSQyxZQUFZLEVBQUUsRUFDZEMsVUFBVSxJQUFJLEVBQ2Y7SUFDQyxNQUFNQyxpQkFBaUI7UUFDckJDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0M7UUFBSU4sV0FBVyxDQUFDLHFEQUFxRCxFQUFFRSxjQUFjLENBQUNELFFBQVEsQ0FBQyxDQUFDLEVBQUVELFdBQVc7a0JBQzNHRDs7Ozs7O0FBR1AsRUFBRTtBQU9LLE1BQU1RLGFBQXdDLENBQUMsRUFBRVIsUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUNoRixxQkFDRSw4REFBQ007UUFBSU4sV0FBVyxDQUFDLEtBQUssRUFBRUEsV0FBVztrQkFDaENEOzs7Ozs7QUFHUCxFQUFFO0FBT0ssTUFBTVMsWUFBc0MsQ0FBQyxFQUFFVCxRQUFRLEVBQUVDLFlBQVksRUFBRSxFQUFFO0lBQzlFLHFCQUNFLDhEQUFDUztRQUFHVCxXQUFXLENBQUMsb0NBQW9DLEVBQUVBLFdBQVc7a0JBQzlERDs7Ozs7O0FBR1AsRUFBRTtBQU9LLE1BQU1XLGNBQTBDLENBQUMsRUFBRVgsUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUNsRixxQkFDRSw4REFBQ007UUFBSU4sV0FBV0E7a0JBQ2JEOzs7Ozs7QUFHUCxFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVqandhbFxcT25lRHJpdmVcXERlc2t0b3BcXFVQREVTQ09cXHVwZGVzY28tZW1zXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxDYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgQ2FyZFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBwYWRkaW5nPzogJ3NtJyB8ICdtZCcgfCAnbGcnO1xufVxuXG5leHBvcnQgY29uc3QgQ2FyZDogUmVhY3QuRkM8Q2FyZFByb3BzPiA9ICh7IFxuICBjaGlsZHJlbiwgXG4gIGNsYXNzTmFtZSA9ICcnLCBcbiAgcGFkZGluZyA9ICdtZCcgXG59KSA9PiB7XG4gIGNvbnN0IHBhZGRpbmdDbGFzc2VzID0ge1xuICAgIHNtOiAncC00JyxcbiAgICBtZDogJ3AtNicsXG4gICAgbGc6ICdwLTgnXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kIGJvcmRlciBib3JkZXItZ3JheS0yMDAgJHtwYWRkaW5nQ2xhc3Nlc1twYWRkaW5nXX0gJHtjbGFzc05hbWV9YH0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5pbnRlcmZhY2UgQ2FyZEhlYWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgY29uc3QgQ2FyZEhlYWRlcjogUmVhY3QuRkM8Q2FyZEhlYWRlclByb3BzPiA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BtYi00ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuaW50ZXJmYWNlIENhcmRUaXRsZVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgY29uc3QgQ2FyZFRpdGxlOiBSZWFjdC5GQzxDYXJkVGl0bGVQcm9wcz4gPSAoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gJycgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxoMyBjbGFzc05hbWU9e2B0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2gzPlxuICApO1xufTtcblxuaW50ZXJmYWNlIENhcmRDb250ZW50UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBDYXJkQ29udGVudDogUmVhY3QuRkM8Q2FyZENvbnRlbnRQcm9wcz4gPSAoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gJycgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNhcmQiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsInBhZGRpbmciLCJwYWRkaW5nQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImRpdiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJoMyIsIkNhcmRDb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Modal.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Modal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Modal: () => (/* binding */ Modal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Modal auto */ \n\nconst Modal = ({ isOpen, onClose, title, children, size = 'md' })=>{\n    if (!isOpen) return null;\n    const sizeClasses = {\n        sm: 'max-w-md',\n        md: 'max-w-lg',\n        lg: 'max-w-2xl',\n        xl: 'max-w-4xl'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-screen items-center justify-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `relative bg-white rounded-lg shadow-xl w-full ${sizeClasses[size]} transform transition-all`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Modal.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUjjwal%5COneDrive%5CDesktop%5CUPDESCO%5Cupdesco-ems&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();