Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9070) msys-2.0.dll+0x1FEBA
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210285FF9, 0007FFFFA028, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA170  0002100690B4 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA450  00021006A49D (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA08C00000 ntdll.dll
7FFA076F0000 KERNEL32.DLL
7FFA06010000 KERNELBASE.dll
7FFA08450000 USER32.dll
7FFA05D50000 win32u.dll
7FFA06AE0000 GDI32.dll
7FFA05D80000 gdi32full.dll
7FFA06620000 msvcp_win.dll
7FFA05EC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA08380000 advapi32.dll
7FFA06EF0000 msvcrt.dll
7FFA07EB0000 sechost.dll
7FFA06C60000 RPCRT4.dll
7FFA05350000 CRYPTBASE.DLL
7FFA06400000 bcryptPrimitives.dll
7FFA06AA0000 IMM32.DLL
