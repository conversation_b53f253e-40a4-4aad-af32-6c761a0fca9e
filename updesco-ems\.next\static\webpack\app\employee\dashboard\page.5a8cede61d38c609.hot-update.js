"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/employee/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/employee/TodoList.tsx":
/*!**********************************************!*\
  !*** ./src/components/employee/TodoList.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TodoList: () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ TodoList auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst TodoList = (param)=>{\n    let { todos, onToggleTodo, onAddTodo } = param;\n    _s();\n    const [isAddingTodo, setIsAddingTodo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTodoTitle, setNewTodoTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newTodoPriority, setNewTodoPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'bg-red-100 text-red-800 border-red-200';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n            case 'low':\n                return 'bg-green-100 text-green-800 border-green-200';\n            default:\n                return 'bg-gray-100 text-gray-800 border-gray-200';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined);\n            case 'in-progress':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-blue-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-gray-400\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleAddTodo = ()=>{\n        if (newTodoTitle.trim()) {\n            onAddTodo(newTodoTitle.trim(), newTodoPriority);\n            setNewTodoTitle('');\n            setNewTodoPriority('medium');\n            setIsAddingTodo(false);\n        }\n    };\n    const pendingTodos = todos.filter((todo)=>todo.status !== 'completed');\n    const completedTodos = todos.filter((todo)=>todo.status === 'completed');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"Todo List\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>setIsAddingTodo(true),\n                            disabled: isAddingTodo,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Add Todo\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    isAddingTodo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Enter todo title...\",\n                                    value: newTodoTitle,\n                                    onChange: (e)=>setNewTodoTitle(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newTodoPriority,\n                                            onChange: (e)=>setNewTodoPriority(e.target.value),\n                                            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: handleAddTodo,\n                                                    children: \"Add\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>{\n                                                        setIsAddingTodo(false);\n                                                        setNewTodoTitle('');\n                                                        setNewTodoPriority('medium');\n                                                    },\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    pendingTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Pending Tasks\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: pendingTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                        children: todo.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    todo.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        children: todo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(todo.priority)),\n                                                children: todo.priority\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined),\n                    completedTodos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Completed Tasks\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: completedTodos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg opacity-75\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onToggleTodo(todo.id),\n                                                className: \"flex-shrink-0\",\n                                                children: getStatusIcon(todo.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600 line-through truncate\",\n                                                    children: todo.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(todo.priority)),\n                                                children: todo.priority\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, todo.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined),\n                    todos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"No todos yet. Add your first task!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UPDESCO\\\\updesco-ems\\\\src\\\\components\\\\employee\\\\TodoList.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TodoList, \"no1A+ddmKmtQjwsqteIZeZWyLSA=\");\n_c = TodoList;\nvar _c;\n$RefreshReg$(_c, \"TodoList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/employee/TodoList.tsx\n"));

/***/ })

});